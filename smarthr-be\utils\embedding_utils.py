# utils/embedding_utils.py
from typing import List
from fastembed import SparseTextEmbedding
from langchain_openai import AzureOpenAIEmbeddings
import os
from dotenv import load_dotenv
from math import sqrt
import numpy as np
import time 
from langchain_openai import AzureOpenAIEmbeddings

load_dotenv()
AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS")

# Initialize embedding models (once, globally)
sparse_embedder = None
# dense_embedder = AzureOpenAIEmbeddings(model=AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS)

def get_sparse_embedder():
    """Get sparse embedder with lazy initialization and error handling."""
    global sparse_embedder
    if sparse_embedder is None:
        try:
            sparse_embedder = SparseTextEmbedding(model_name="Qdrant/bm42-all-minilm-l6-v2-attentions")
        except Exception as e:
            print(f"Warning: Could not initialize sparse embedder: {e}")
            print("Sparse embeddings will not be available.")
            sparse_embedder = False  # Mark as failed to avoid retrying
    return sparse_embedder if sparse_embedder is not False else None

# azure_endpoint = "https://openai-smarthr.openai.azure.com/openai/deployments/text-embedding-3-small-smarthr/embeddings?api-version=2023-05-15"
# api_key = "********************************"
# model_openai = AzureOpenAIEmbeddings(azure_endpoint=azure_endpoint, api_key=api_key)


def get_embeddings(text: str):
    """ Generate both sparse and dense embeddings for the given text.
    Uses lazy initialization for sparse embedder with error handling.
    """
    # Generate sparse embedding with error handling
    sparse_embedding = None
    embedder = get_sparse_embedder()
    if embedder:
        try:
            sparse_embedding = list(embedder.query_embed(text))[0]
        except Exception as e:
            print(f"Warning: Failed to generate sparse embedding: {e}")
            sparse_embedding = None

    # Generate dense embedding
    dense_embedding = generate_openai_embedding(text)

    # print("Generated embeddings:*********************************************")
    # print("Generated embeddings:*********************************************")
    # print("sparse_embedding:", sparse_embedding)
    # print("dense_embedding:", dense_embedding[:10])
    return sparse_embedding, dense_embedding


def rerank_candidates():
    pass


def explain_selection():
    pass


# def generate_openai_embedding(text):
#     max_retries = 3
#     retries = 0
#     while retries < max_retries:
#         try:
#             # Generate embedding
#             embedding = model_openai.embed_documents([text])[0]
#             return embedding
#         except Exception as e:
#             retries += 1
#             print(f"Error generating embedding for text: {text[:30]}...: {e}")

#             # If retries exceed max_retries, log and return None to continue processing
#             if retries >= max_retries:
#                 print(f"Max retries exceeded for embedding generation. Skipping...")
#                 return None


def clean_sparse_embedding(sparse_emb):
    if sparse_emb is None:
        return {}  # Return empty dict if no sparse embedding available

    raw_dict = sparse_emb.as_dict()
    for k, v in raw_dict.items():
        if isinstance(v, np.ndarray):
            raw_dict[k] = v.tolist()
        elif isinstance(v, dict):
            # Also convert any nested ndarrays
            for nk, nv in v.items():
                if isinstance(nv, np.ndarray):
                    v[nk] = nv.tolist()
    return raw_dict


def cosine_similarity_sparse(vec1: dict, vec2: dict) -> float:
    # Dot product
    dot_product = sum(vec1.get(k, 0) * vec2.get(k, 0) for k in set(vec1) | set(vec2))

    # Norms
    norm1 = sqrt(sum(v**2 for v in vec1.values()))
    norm2 = sqrt(sum(v**2 for v in vec2.values()))

    # Avoid division by zero
    if norm1 == 0 or norm2 == 0:
        return 0.0

    return dot_product / (norm1 * norm2)


def generate_openai_embedding(text: str) -> List[float]:
    # print(f"Generating OpenAI embedding for text: {text}")
    model_openai = AzureOpenAIEmbeddings(model=AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS)
    max_retries = 3
    retries = 0
    while retries < max_retries:
        try:
            embedding = model_openai.embed_query(text)
            # embedding = model_openai.embed_documents([text])[0]
            return embedding
        except Exception as e:
            retries += 1
            print(f"Error generating embedding for text: {text[:30]}...: {e}")

            # If retries exceed max_retries, log and return None to continue processing
            if retries >= max_retries:
                print("Max retries exceeded for embedding generation. Skipping...")
                return None


def format_vector(vector: List[float]) -> str:
    # Format vector as a PostgreSQL array if needed,
    # or store as a bytea if using binary.
    # pgvector typically accepts python lists directly if using psycopg2 `execute` with parameters.
    # If you face issues, convert to tuple or something. For now, assume direct:
    return vector


def flatten_json_to_text(obj):
    if isinstance(obj, dict):
        return " ".join(flatten_json_to_text(v) for v in obj.values())
    elif isinstance(obj, list):
        return " ".join(flatten_json_to_text(v) for v in obj)
    else:
        return str(obj)
