# candidates_controller.py

# 1. Standard library imports
import os
import io
import json
import uuid
import logging
from contextlib import contextmanager
from typing import List, Optional, <PERSON><PERSON>
from datetime import datetime, time

# 2. Third-party imports
import psycopg2
from psycopg2.extras import J<PERSON>
from psycopg2 import sql
from fastapi import HTTPException
import pdfkit
from jinja2 import ChainableUndefined, Environment, BaseLoader
from pdf2docx import Converter

# 3. Internal imports
from core.config import settings
from config.config import CANDIDATE_RESUME_PAGE_OPTIONS
from models.candidate import Candidate, CandidateCreate, CandidateFilters, CandidateUpdate
from models.validate_models import ValidateCandidateItem, ValidateCandidateRequest, ValidateCandidateResponse
from templates.candidates_templates.candidates_arroyo.json_column import prepare_candidate_for_embedding
from utils.embedding_utils import clean_sparse_embedding, cosine_similarity_sparse, format_vector, get_embeddings
from utils import match_evaluations as match_functions

# Telemetry Section
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


# Database connection context manager
@contextmanager
def get_cursor():
    """
    Context manager for PostgreSQL cursor with error handling.
    """
    conn = None
    try:
        conn = psycopg2.connect(
            settings.DATABASE_URL,
            connect_timeout=120,
            options="-c statement_timeout=120000",
            keepalives_idle=30
        )
        with conn:
            with conn.cursor() as cur:
                yield cur
    except psycopg2.Error as e:
        logger.error(f"Database error occurred in get_cursor: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    finally:
        if conn:
            conn.close()


# Create a new candidate in the database.
def create_candidate(candidate_data: CandidateCreate) -> Candidate:
    """    
    Create a new candidate in the database.
    Args:
        candidate_data (CandidateCreate): The candidate data to create.
    Returns:
        Candidate: The created candidate object.
    """
    with get_cursor() as cur:
        # Check if roles are None or empty
        if candidate_data.candidate_info.get("roles") is None or len(candidate_data.candidate_info.get("roles", [])) == 0:
            roles = get_roles_by_candidate(candidate_data.candidate_info)
            candidate_data.candidate_info["roles"] = roles if roles else []
        else:
            candidate_data.candidate_info["roles"] = _dedupe_and_clean(candidate_data.candidate_info["roles"])
        # Prepare text to embed
        embed_text = prepare_candidate_for_embedding(candidate_data.candidate_info)
        sparse_embedding, dense_embedding = get_embeddings(str(candidate_data.candidate_info))
        embedding_vector = format_vector(dense_embedding) if dense_embedding else None
        sparse_dict = clean_sparse_embedding(sparse_embedding)

        cur.execute(
            """
                INSERT INTO candidates_smarthr 
                (proj_id, candidate_info, suggested_positions, analysis_status, to_be_embebbed, embedding, sparse_embedding, created_by, updated_by, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                RETURNING id, proj_id, candidate_info, suggested_positions, analysis_status, last_matching, is_active, reason_info, updated_by, updated_at, created_by, created_at
                """,
            (
                candidate_data.proj_id,
                Json(candidate_data.candidate_info),
                Json(candidate_data.suggested_positions),
                candidate_data.analysis_status,
                embed_text,
                embedding_vector,
                Json(sparse_dict),
                candidate_data.created_by,
                candidate_data.created_by,  # Assuming created_by is the same as updated_by for creation
            ),
        )

        row = cur.fetchone()

    if not row:
        return None

    # Ensure suggested_positions is a list
    suggested_positions = row[3]
    if suggested_positions is not None and not isinstance(suggested_positions, list):
        # If it's a dict or other type, wrap it in a list or set to empty list
        suggested_positions = [suggested_positions] if isinstance(suggested_positions, dict) else []

    return Candidate(
        id=str(row[0]),
        proj_id=str(row[1]),
        candidate_info=row[2],
        suggested_positions=suggested_positions,
        analysis_status=row[4],
        last_matching=row[5],
        is_active=row[6],
        reason_info=row[7],
        updated_by=row[8],
        updated_at=row[9],
        created_by=row[10],
        created_at=row[11]
    )


# Update an existing candidate in the database.
def update_candidate(candidate_data: CandidateUpdate) -> Candidate:
    """
    Update an existing candidate in the database.
    Args:
        candidate_data (CandidateUpdate): The candidate data to update.
    Returns:
        Candidate: The updated candidate object.
    """
    with get_cursor() as cur:        
        # Check if roles are None or empty
        if candidate_data.candidate_info.get("roles") is None or len(candidate_data.candidate_info.get("roles", [])) == 0:
            roles = get_roles_by_candidate(candidate_data.candidate_info)
            candidate_data.candidate_info["roles"] = roles if roles else []
        else:
            candidate_data.candidate_info["roles"] = _dedupe_and_clean(candidate_data.candidate_info["roles"])
        # Prepare text to embed
        # Implement get_candidate_embedding_text() as before:
        embed_text = prepare_candidate_for_embedding(candidate_data.candidate_info)
        candidate_embebbed_text, embedding_vector, sparse_embedding = get_candidate_embedding_data_by_id(candidate_data.id)
        # print("candidate_embebbed_text", candidate_embebbed_text)
        # print("embed_text", embed_text)
        if (str(candidate_embebbed_text) != embed_text):
            sparse_embedding, dense_embedding = get_embeddings(str(candidate_data.candidate_info))
            embedding_vector = format_vector(dense_embedding) if dense_embedding else None
            sparse_dict = clean_sparse_embedding(sparse_embedding)

        params = [Json(candidate_data.candidate_info),
                  Json(candidate_data.suggested_positions)]

        sqlQuery = """
            UPDATE candidates_smarthr SET
                candidate_info = %s, 
                suggested_positions = %s,
            """

        if (candidate_embebbed_text != embed_text):
            sqlQuery += """
                            to_be_embebbed= %s, 
                            embedding = %s,
                            sparse_embedding = %s,
                        """
            params.extend([embed_text, embedding_vector, Json(sparse_dict)])

        sqlQuery += """
                    updated_by = %s,
                    updated_at = Now()
                    Where id = %s
                    RETURNING id, proj_id, candidate_info, suggested_positions, analysis_status, last_matching, is_active, reason_info, updated_by, updated_at, created_by, created_at
                """
        params.append(candidate_data.updated_by)
        params.append(candidate_data.id)

        cur.execute(sqlQuery, params)
        row = cur.fetchone()

    if not row:
        return None

    # Ensure suggested_positions is a list
    suggested_positions = row[3]
    if suggested_positions is not None and not isinstance(suggested_positions, list):
        # If it's a dict or other type, wrap it in a list or set to empty list
        suggested_positions = [suggested_positions] if isinstance(suggested_positions, dict) else []

    return Candidate(
        id=str(row[0]),
        proj_id=str(row[1]),
        candidate_info=row[2],
        suggested_positions=suggested_positions,
        analysis_status=row[4],
        last_matching=row[5],
        is_active=row[6],
        reason_info=row[7],
        updated_by=row[8],
        updated_at=row[9],
        created_by=row[10],
        created_at=row[11]
    )


# Deduplicate and clean roles from the candidate's information.
def _dedupe_and_clean(roles) -> List[str]:
    """
    Deduplicate and clean roles from the candidate's information.
    Args:
        roles (List[str]): List of roles to deduplicate and clean.
    Returns:
        List[str]: Cleaned list of unique roles.
    """
    # preserve order, drop None/empty/whitespace-only
    seen = set()
    cleaned = []
    for r in roles:
        # Fix: Lumus returns roles as dicts sometimes [{"role": "Cloud Administrator"}, {"role": "DevOps Engineer"}]
        if isinstance(r, dict):
            if r.get('role'):
                r = r['role']
        if not isinstance(r, str):
            continue
        s = r.strip()
        if not s or s in seen:
            continue
        seen.add(s)
        cleaned.append(s)
    return cleaned


# Extracts roles from the candidate's information.
def get_roles_by_candidate(candidate: dict) -> List[str]:
    """
    Extracts roles from the candidate's information.
    If no valid roles are found, it uses the evaluate function to get roles.
    Removes empty/null and duplicates from the final result.
    """
    # print("Extracting roles for candidate:", candidate.get("id"), '*********************************')
    # gather raw roles from work experience
    raw_roles = [
        job.get("job_title")
        for job in candidate.get("work_experience", [])
        if isinstance(job, dict)
    ]
    candidate_roles = _dedupe_and_clean(raw_roles)
    # print("Initial extracted roles:", candidate_roles)
    if not candidate_roles or len(candidate_roles) == 0:
        # fallback to evaluator, then clean again
        fallback = match_functions.get_roles_to_candidates(candidate) or []
        # print("Roles from evaluator:", fallback)
        candidate_roles = _dedupe_and_clean(fallback)
        # print("Roles from evaluator:", candidate_roles)
    return candidate_roles


# Fetches the embedded text, embedding vector, and sparse embedding dictionary for a candidate by their ID.
def get_candidate_embedding_data_by_id(candidate_id: str) -> Optional[Tuple[str, List[float], dict]]:
    """
    Fetches the embedded text, embedding vector, and sparse embedding dictionary for a candidate by their ID.
    Args:
        candidate_id (str): The unique identifier of the candidate.
    Returns:
        Optional[Tuple[str, List[float], dict]]:
            A tuple containing:
                - to_be_embebbed (str): The text to be embedded.
                - embedding (List[float]): The embedding vector.
                - sparse_embedding (dict): The sparse embedding dictionary.
            Returns None if the candidate is not found.
    """
    with get_cursor() as cur:
        cur.execute(
            """
            SELECT to_be_embebbed, embedding, sparse_embedding FROM candidates_smarthr WHERE id::text=%s
            """,
            (candidate_id,),
        )
        row = cur.fetchone()
    if not row:
        return None

    return str(row[0]), row[1], row[2]


# Retrieves a candidate from the database by their ID.
def get_candidate_by_id(candidate_id: str) -> Optional[Candidate]:
    '''
    Retrieves a candidate from the database by their ID.
    Args:
        candidate_id (str): The unique identifier of the candidate.
    Returns:
        Optional[Candidate]: The candidate object if found, None otherwise.
    '''
    with get_cursor() as cur:
        cur.execute(
            """
            SELECT id, proj_id, candidate_info, suggested_positions, analysis_status, last_matching, is_active, reason_info, updated_by, updated_at, created_by, created_at
            FROM candidates_smarthr WHERE id::text=%s
        """,
            (candidate_id,),
        )
        row = cur.fetchone()
        if not row:
            return None

    # Ensure suggested_positions is a list
    suggested_positions = row[3]
    if suggested_positions is not None and not isinstance(suggested_positions, list):
        # If it's a dict or other type, wrap it in a list or set to empty list
        suggested_positions = [suggested_positions] if isinstance(suggested_positions, dict) else []

    return Candidate(
        id=str(row[0]),
        proj_id=str(row[1]),
        candidate_info=row[2],
        suggested_positions=suggested_positions,
        analysis_status=row[4],
        last_matching=row[5],
        is_active=row[6],
        reason_info=row[7],
        updated_by=row[8],
        updated_at=row[9],
        created_by=row[10],
        created_at=row[11]
    )


# Fetch all candidates from the database.
def fetch_all_candidates() -> List[Candidate]:
    """
    Fetch all candidates from the database.
    Returns:
        List[Candidate]: A list of all candidates.
    """
    with get_cursor() as cur:
        cur.execute(
            """
            SELECT id, proj_id, candidate_info, suggested_positions, analysis_status, last_matching, is_active, reason_info, updated_by, updated_at, created_by, created_at
            FROM candidates_smarthr WHERE is_deleted = false
            ORDER BY created_at DESC
        """
        )
        rows = cur.fetchall()
        candidates = []
        for row in rows:
            candidates.append(
                Candidate(
                    id=str(row[0]),
                    proj_id=str(row[1]),
                    candidate_info=row[2],
                    suggested_positions=row[3],
                    analysis_status=row[4],
                    last_matching=row[5],
                    is_active=row[6],
                    reason_info=row[7],
                    updated_by=row[8],
                    updated_at=row[9],
                    created_by=row[10],
                    created_at=row[11]
                )
            )
        return candidates


# Fetch a paginated list of candidates from the database.
def get_candidates_page(page: int, chunk_size: int = 20, filters: Optional[CandidateFilters] = None) -> List[Candidate]:
    """
    Fetch a paginated list of candidates from the database.
    Args:
        page (int): The page number to fetch (1-based).
        chunk_size (int): The number of candidates per page.
        filters (Optional[CandidateFilters]): Optional filters to apply.
    Returns:
        List[Candidate]: List of candidates for the requested page.
    """
    if page < 1:
        raise ValueError("Page number must be greater than or equal to 1.")
    if not (1 <= chunk_size <= 1000):
        raise ValueError("Chunk size must be between 1 and 1000.")

    offset = (page - 1) * chunk_size
    where_clause, params = build_where_clause(filters)
    params += [chunk_size, offset]

    query = sql.SQL("""
        SELECT
            id, proj_id, candidate_info, suggested_positions, analysis_status,
            last_matching, is_active, reason_info, updated_by, updated_at,
            created_by, created_at
        FROM candidates_smarthr
        {where_clause}
        ORDER BY created_at DESC
        LIMIT %s OFFSET %s
    """).format(where_clause=where_clause)

    def row_to_candidate(row) -> Candidate:
        # Ensure suggested_positions is a list
        suggested_positions = row[3]
        if suggested_positions is not None and not isinstance(suggested_positions, list):
            # If it's a dict or other type, wrap it in a list or set to empty list
            suggested_positions = [suggested_positions] if isinstance(suggested_positions, dict) else []

        return Candidate(
            id=str(row[0]),
            proj_id=str(row[1]),
            candidate_info=row[2],
            suggested_positions=suggested_positions,
            analysis_status=row[4],
            last_matching=row[5],
            is_active=row[6],
            reason_info=row[7],
            updated_by=row[8],
            updated_at=row[9],
            created_by=row[10],
            created_at=row[11]
        )

    with get_cursor() as cur:
        cur.execute(query, params)
        rows = cur.fetchall()
        return [row_to_candidate(row) for row in rows]


# Returns the total number of candidates in the database, optionally filtered by the provided filters.
def get_total_candidates(filters: Optional[CandidateFilters] = None) -> int:
    """
    Returns the total number of candidates in the database, optionally filtered by the provided filters.
    Args:
        filters (Optional[CandidateFilters]): Optional filters to apply to the count query.
    Returns:
        int: The total count of candidates matching the filters.
    """
    where_clause, params = build_where_clause(filters)
    query = sql.SQL("SELECT COUNT(*) FROM candidates_smarthr {where_clause}").format(where_clause=where_clause)

    with get_cursor() as cur:
        cur.execute(query, params)
        row = cur.fetchone()

    return row[0] if row else 0


# Validate a candidate against existing candidates in the database using Levenshtein distance on specified keys, returning the closest matches.
def validate_candidate(request: ValidateCandidateRequest) -> ValidateCandidateResponse:
    """
    Validate a candidate against existing candidates in the database using Levenshtein distance on specified keys,
    returning the closest matches.

    Parameters
    ----------
    request : ValidateCandidateRequest
        The request containing candidate information, project ID, and keys to evaluate.

    Returns
    -------
    ValidateCandidateResponse
        Response containing a list of matching candidates with their average distances.

    Raises
    ------
    HTTPException
        If required parameters are missing or a database error occurs.
    """
    if not request.project_id:
        raise HTTPException(status_code=400, detail="No valid project_id provided.")

    candidate_info = request.candidate_info
    keys_to_evaluate = request.keys_to_eval

    levenshtein_expressions = []
    candidate_values = []

    # Build Levenshtein expressions and extract values for each key path
    for key_path in keys_to_evaluate:
        path_parts = key_path.split(".")
        json_path = "{" + ",".join(path_parts) + "}"

        # Extract value from candidate_info using the path
        value = candidate_info
        for part in path_parts:
            if isinstance(value, dict) and part in value:
                value = value[part]
            else:
                value = ""
                break

        if not isinstance(value, str):
            value = str(value) if value is not None else ""

        candidate_values.append(value.lower())
        levenshtein_expressions.append(f"levenshtein(c.candidate_info #>> '{json_path}', %s)")

    if not levenshtein_expressions:
        raise HTTPException(status_code=400, detail="No valid keys to evaluate were provided.")

    avg_expression = f"({' + '.join(levenshtein_expressions)})::float / {len(levenshtein_expressions)}"

    sql_query = f"""
        SELECT
            c.id,
            {avg_expression} AS average_distance,
            candidate_info
        FROM
            candidates_smarthr c
        WHERE
            c.is_deleted = false
            AND c.proj_id = %s
            AND ({avg_expression}) < 10
        ORDER BY
            average_distance ASC
        LIMIT 4;
    """

    # Prepare parameters: first for Levenshtein, then project_id, then again for Levenshtein (for avg_expr in WHERE)
    params = candidate_values + [request.project_id] + candidate_values

    try:
        with get_cursor() as cur:
            cur.execute(sql_query, params)
            rows = cur.fetchall()

        matching_candidates = [
            ValidateCandidateItem(
                id=str(row["id"]),
                average_distance=row["average_distance"],
                candidate_info=row["candidate_info"],
            )
            for row in rows
        ]

        return ValidateCandidateResponse(matching_candidates=matching_candidates)

    except HTTPException as http_exc:
        logger.error(f"HTTPException in validate_candidate: {http_exc}")
        raise http_exc
    except Exception as exc:
        logger.error(f"Error in validate_candidate: {exc}")
        raise HTTPException(status_code=500, detail=str(exc))


# Generate a PDF resume for a candidate using an HTML template.
def create_candidate_pdf(candidate: Candidate) -> bytes:
    """
    Generate a PDF resume for a candidate using an HTML template.
    Args:
        candidate (Candidate): The candidate object containing resume information.
    Returns:
        bytes: The generated PDF data as a byte string.
    Raises:
        HTTPException: If the template file cannot be read or PDF generation fails.
    """
    try:
        template_path = os.path.join(os.getcwd(), 'templates', 'html', 'candidate_resume.html')
        logger.info(f"Loading HTML template from path: {template_path}")

        with open(template_path, 'r', encoding='utf-8') as template_file:
            html_template_content = template_file.read()

        logger.debug("HTML template content loaded successfully.")

        jinja_env = Environment(loader=BaseLoader(), undefined=ChainableUndefined)
        template = jinja_env.from_string(html_template_content)
        rendered_html = template.render(candidate=candidate.candidate_info, current_date=datetime.now())

        logger.debug("HTML template rendered successfully.")

        pdf_options = CANDIDATE_RESUME_PAGE_OPTIONS
        pdf_data = pdfkit.from_string(rendered_html, False, options=pdf_options)

        logger.info(f"Generated PDF data length: {len(pdf_data)} bytes.")

        return pdf_data

    except Exception as e:
        logger.error(f"Error generating candidate PDF: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating candidate PDF: {str(e)}")


# Generate a DOCX resume for a candidate by first creating a PDF and then converting it to DOCX format.
def create_candidate_docx(candidate: Candidate) -> memoryview:
    """
    Generate a DOCX resume for a candidate by first creating a PDF and then converting it to DOCX format.
    Args:
        candidate (Candidate): The candidate object containing resume information.
    Returns:
        memoryview: The generated DOCX data as a memoryview object.
    Raises:
        HTTPException: If PDF generation or DOCX conversion fails.
    """
    try:
        output_stream = io.BytesIO()

        # Generate PDF from candidate data
        candidate_pdf_bytes = create_candidate_pdf(candidate)
        pdf_stream = io.BytesIO(candidate_pdf_bytes)

        # Convert PDF to DOCX using pdf2docx
        converter = Converter(stream=pdf_stream)
        converter.convert(output_stream)
        converter.close()

        return output_stream.getbuffer()

    except Exception as exc:
        logger.error(f"Error occurred while creating candidate DOCX: {str(exc)}")
        raise HTTPException(status_code=500, detail=f"Error creating candidate DOCX: {str(exc)}")


# Update the active status and reason information for a candidate.
def change_candidate_status(candidate_id: str, is_active: bool, reason_info: dict) -> Candidate:
    """
    Update the active status and reason information for a candidate.
    Args:
        candidate_id (str): The unique identifier of the candidate.
        is_active (bool): The new active status to set for the candidate.
        reason_info (dict): The reason for the status change.
    Returns:
        Candidate: The updated candidate object, or None if not found.
    Raises:
        HTTPException: If a database error occurs or the update fails.
    """
    with get_cursor() as cur:
        logger.info("Changing candidate status for candidate_id: %s", candidate_id)
        sql_query = """
            UPDATE candidates_smarthr SET
                is_active = %s,
                reason_info = %s,
                updated_at = NOW()
            WHERE id = %s
            RETURNING id, proj_id, candidate_info, suggested_positions, analysis_status, last_matching, is_active, reason_info, updated_by, updated_at, created_by, created_at
        """
        cur.execute(
            sql_query,
            (
                is_active,
                Json(reason_info),
                candidate_id,
            ),
        )
        row = cur.fetchone()

    if not row:
        logger.info("Candidate not updated (not found): %s", candidate_id)
        return None

    logger.info("Candidate status updated successfully: %s", candidate_id)

    # Ensure suggested_positions is a list
    suggested_positions = row[3]
    if suggested_positions is not None and not isinstance(suggested_positions, list):
        # If it's a dict or other type, wrap it in a list or set to empty list
        suggested_positions = [suggested_positions] if isinstance(suggested_positions, dict) else []

    return Candidate(
        id=str(row[0]),
        proj_id=str(row[1]),
        candidate_info=row[2],
        suggested_positions=suggested_positions,
        analysis_status=row[4],
        last_matching=row[5],
        is_active=row[6],
        reason_info=row[7],
        updated_by=row[8],
        updated_at=row[9],
        created_by=row[10],
        created_at=row[11]
    )


# Delete candidate, set is_deleted = true, set reason_info
def delete_candidate(candidate_id: str, reason_info: dict) -> bool:
    """
    Soft-delete a candidate by setting is_deleted to True and updating the reason information.
    Args:
        candidate_id (str): The unique identifier of the candidate to delete.
        reason_info (dict): The reason for deleting the candidate.
    Returns:
        bool: True if the candidate was successfully marked as deleted, False otherwise.
    """
    sql_query = """
        UPDATE candidates_smarthr SET
            is_deleted = true,
            reason_info = %s,
            updated_at = NOW()
        WHERE id = %s
        RETURNING id
    """
    with get_cursor() as cur:
        cur.execute(sql_query, (Json(reason_info), candidate_id))
        row = cur.fetchone()
    return bool(row)


# Retrieve a candidate from the database by their email address.
def get_candidate_by_email(email: str) -> Optional[Candidate]:
    """
    Retrieve a candidate from the database by their email address.
    Args:
        email (str): The email address to search for.
    Returns:
        Optional[Candidate]: The candidate object if found, None otherwise.
    Raises:
        HTTPException: If the email is missing or invalid.
    """
    if not email or not isinstance(email, str) or len(email.strip()) == 0:
        raise HTTPException(status_code=400, detail="A valid email is required.")

    query = """
        SELECT id, proj_id, candidate_info, suggested_positions, analysis_status, last_matching, is_active, reason_info, updated_by, updated_at, created_by, created_at
        FROM candidates_smarthr
        WHERE is_deleted = false
        AND EXISTS (
            SELECT 1
            FROM unnest(string_to_array(LOWER(TRIM(candidate_info #>> '{personal_info,email}')), ';')) AS candidate_email
            WHERE TRIM(candidate_email) = LOWER(TRIM(%s))
        )
    """

    with get_cursor() as cur:
        cur.execute(query, (email.lower(),))
        row = cur.fetchone()

    if not row:
        return None

    # Ensure suggested_positions is a list
    suggested_positions = row[3]
    if suggested_positions is not None and not isinstance(suggested_positions, list):
        # If it's a dict or other type, wrap it in a list or set to empty list
        suggested_positions = [suggested_positions] if isinstance(suggested_positions, dict) else []

    return Candidate(
        id=str(row[0]),
        proj_id=str(row[1]),
        candidate_info=row[2],
        suggested_positions=suggested_positions,
        analysis_status=row[4],
        last_matching=row[5],
        is_active=row[6],
        reason_info=row[7],
        updated_by=row[8],
        updated_at=row[9],
        created_by=row[10],
        created_at=row[11]
    )


# Test the LLM match logic for a candidate and position using a specified model.
def test_llm_match_logic(model_name: str, position_id: str, candidate_id: str = None):
    """
    Test the LLM match logic for a candidate and position using a specified model.
    This function retrieves the processed text and embedding for a given position and either:
    - Uses a provided candidate ID to fetch the candidate's processed text and perform both parallel and batch LLM evaluations.
    - Or, if no candidate ID is provided, finds the most similar candidate by embedding and performs the same evaluations.
    Args:
        model_name (str): The name of the LLM model to use for evaluation.
        position_id (str): The ID of the position to match against.
        candidate_id (str, optional): The ID of the candidate to use. If not provided, the most similar candidate is selected.
    Returns:
        dict: A dictionary containing the results of both parallel and batch LLM evaluations.
    Raises:
        HTTPException: If the position or candidate is not found.
    """
    with get_cursor() as cur:
        # Retrieve the position's processed text and embedding
        cur.execute(
            "SELECT to_be_embebbed, embedding FROM positions_smarthr WHERE id=%s",
            (position_id,),
        )
        position_row = cur.fetchone()
        if not position_row:
            raise HTTPException(status_code=404, detail="Position not found")

        processed_position = position_row[0]
        position_embedding = position_row[1]

        # If candidate_id is provided, fetch candidate and perform analysis
        if candidate_id:
            try:
                logger.info("Candidate id provided: %s-----------------------\n", candidate_id)
                cur.execute(
                    "SELECT to_be_embebbed, embedding FROM candidates_smarthr WHERE id=%s",
                    (candidate_id,),
                )
                candidate_row = cur.fetchone()
                if not candidate_row:
                    raise HTTPException(status_code=404, detail="Candidate not found")
                processed_candidate = candidate_row[0]

                models_order = [model_name]
                # Parallel simulation
                analysis_parallel = match_functions.evaluate_candidate(
                    processed_candidate, processed_position, models_order=models_order,
                )
                response_parallel = {
                    "candidate_id": candidate_id,
                    "position_id": position_id,
                    "model_name": model_name,
                    "analysis": dict(analysis_parallel)
                }
                # Batch simulation
                analysis_batch = match_functions.evaluate_candidates_batch(
                    [processed_candidate], processed_position, models_order=models_order,
                )
                analysis_batch_result = analysis_batch.candidates_analysis[0]
                response_batch = {
                    "candidate_id": candidate_id,
                    "position_id": position_id,
                    "model_name": model_name,
                    "analysis": dict(analysis_batch_result)
                }
                return {"parallel": response_parallel, "batch": response_batch}
            except Exception as e:
                logger.warning(f"Error occurred while fetching candidate by id: {str(e)}")

        # If candidate_id is not provided, find the most similar candidate by embedding
        cur.execute(
            """
            SELECT id, to_be_embebbed FROM candidates_smarthr
            WHERE is_deleted = false AND is_active = true
            ORDER BY 1 - (embedding <=> %s) DESC
            LIMIT 1
            """,
            (position_embedding,),
        )
        candidate_row = cur.fetchone()
        if not candidate_row:
            raise HTTPException(status_code=404, detail="No candidates found")

        selected_candidate_id = candidate_row[0]
        processed_candidate = candidate_row[1]

    models_order = [model_name]
    # Parallel simulation
    analysis_parallel = match_functions.evaluate_candidate(
        processed_candidate, processed_position, models_order=models_order,
    )
    parallel_response = {
        "candidate_id": selected_candidate_id,
        "position_id": position_id,
        "model_name": model_name,
        "analysis": dict(analysis_parallel)
    }
    # Batch simulation
    analysis_batch = match_functions.evaluate_candidates_batch(
        [processed_candidate], processed_position, models_order=models_order,
    )
    analysis_batch_result = analysis_batch.candidates_analysis[0]
    batch_response = {
        "candidate_id": selected_candidate_id,
        "position_id": position_id,
        "model_name": model_name,
        "analysis": dict(analysis_batch_result)
    }

    return {"parallel": parallel_response, "batch": batch_response}


# Retrieve a list of distinct job titles from candidates' work experience.
def get_job_titles() -> List[str]:
    """
    Retrieve a list of distinct job titles from candidates' work experience.
    Returns
    -------
    List[str]
        A list of unique job titles found in the candidates' work experience.
    Notes
    -----
    This function checks up to the first five work experience entries for each candidate.
    Only non-empty and non-null job titles are included in the result.
    """
    query = """
        SELECT DISTINCT job_title
        FROM (
            SELECT 
                COALESCE(
                    NULLIF(candidate_info->'work_experience'->0->>'job_title', ''),
                    NULLIF(candidate_info->'work_experience'->1->>'job_title', ''),
                    NULLIF(candidate_info->'work_experience'->2->>'job_title', ''),
                    NULLIF(candidate_info->'work_experience'->3->>'job_title', ''),
                    NULLIF(candidate_info->'work_experience'->4->>'job_title', '')
                ) AS job_title
            FROM public.candidates_smarthr
            WHERE is_deleted = false
        ) AS sub
        WHERE job_title IS NOT NULL
    """
    with get_cursor() as cur:
        cur.execute(query)
        rows = cur.fetchall()
    return [row[0] for row in rows]


# Retrieve a list of distinct countries from candidates' personal information.
def get_countries() -> List[str]:
    """
    Retrieve a list of distinct countries from candidates' personal information.
    Returns
    -------
    List[str]
        A list of unique country names found in the candidates' personal_info.
    Notes
    -----
    Only non-empty and non-null country values are included in the result.
    """
    query = """
        SELECT DISTINCT country
        FROM (
            SELECT 
                NULLIF(candidate_info->'personal_info'->>'country', '') AS country
            FROM public.candidates_smarthr
            WHERE is_deleted = false
        ) AS sub
        WHERE country IS NOT NULL
    """
    with get_cursor() as cur:
        cur.execute(query)
        rows = cur.fetchall()
    return [row[0] for row in rows]


# Construct a dynamic SQL WHERE clause and parameters for filtering candidates.
def build_where_clause(filters: CandidateFilters) -> Tuple[sql.SQL, list]:
    """
    Construct a dynamic SQL WHERE clause and parameters for filtering candidates.
    Parameters
    ----------
    filters : CandidateFilters
        An object containing filter criteria such as search term, status, country, role, and date range.
    Returns
    -------
    Tuple[sql.SQL, list]
        A tuple containing the SQL WHERE clause (as a psycopg2.sql.SQL object) and a list of parameters.
    Notes
    -----
    - The function always filters out deleted candidates (is_deleted = false).
    - If a search term is provided, it performs a case-insensitive search on the candidate_info JSON.
    - Additional filters (status, country, role, created_from, created_to) are appended as needed.
    - Date filters are normalized to cover the full days.
    """
    where_clause = sql.SQL("WHERE is_deleted = false")
    params = []

    if filters:
        # Search term filter (case-insensitive, on candidate_info as text)
        if getattr(filters, "search_term", None) and filters.search_term.strip():
            where_clause += sql.SQL(
                " AND ("
                "LOWER(TRIM(created_by)) LIKE %s "
                "OR LOWER(TRIM(candidate_info #>> '{personal_info,full_name}')) LIKE %s "
                "OR EXISTS ("
                "    SELECT 1 "
                "    FROM unnest(string_to_array(candidate_info #>> '{personal_info,email}', ';')) AS candidate_email "
                "    WHERE LOWER(TRIM(candidate_email)) LIKE %s"
                ")"
                ")"
            )
            params.append(f"%{filters.search_term.lower().strip()}%")
            params.append(f"%{filters.search_term.lower().strip()}%")
            params.append(f"%{filters.search_term.lower().strip()}%")

        # Status filter (active/inactive)
        if getattr(filters, "status", None) is not None:
            where_clause += sql.SQL(" AND is_active = %s")
            params.append(filters.status)

        # Country filter (exact match)
        if getattr(filters, "country", None):
            where_clause += sql.SQL(" AND candidate_info #>> '{personal_info,country}' = %s")
            params.append(filters.country)

        if getattr(filters, "created_by", None):
            where_clause += sql.SQL(" AND created_by = %s")
            params.append(filters.created_by)

        # Role filter (case-insensitive, partial match in work_experience)
        if getattr(filters, "role", None):
            where_clause += sql.SQL(
                " AND EXISTS (SELECT 1 FROM jsonb_array_elements(candidate_info->'work_experience') AS we "
                "WHERE lower(we->>'job_title') LIKE %s)"
            )
            params.append(f"%{filters.role.lower()}%")

        # Date range filter (created_at between start and end of days)
        created_from = getattr(filters, "created_from", None)
        created_to = getattr(filters, "created_to", None)
        if created_from and created_to:
            start_datetime = datetime.combine(created_from.date(), time.min)
            end_datetime = datetime.combine(created_to.date(), time.max)
            where_clause += sql.SQL(" AND created_at BETWEEN %s AND %s")
            params.extend([start_datetime, end_datetime])

    return where_clause, params


# Update all candidates in the database by reprocessing their information.
def update_all_candidates() -> List[Candidate]:
    """
    Update all candidates in the database by reprocessing their information.
    This function fetches all non-deleted candidates, updates each one using the
    `update_candidate` function (which may refresh roles, embeddings, etc.), and
    returns a list of the updated candidate objects.
    Returns
    -------
    List[Candidate]
        A list of updated Candidate objects.
    """
    all_candidates = fetch_all_candidates()
    updated_candidates = []
    for candidate in all_candidates:
        candidate_update = CandidateUpdate(
            id=candidate.id,
            proj_id=candidate.proj_id,
            candidate_info=candidate.candidate_info,
            suggested_positions=candidate.suggested_positions,
            analysis_status=candidate.analysis_status,
            updated_by=candidate.updated_by or 'system'
        )
        updated_candidate = update_candidate(candidate_update)
        updated_candidates.append(updated_candidate)
    return updated_candidates


# Retrieve and evaluate all positions for a given candidate, returning various similarity scores and analyses.
def candidate_match_positions(candidate_id: str) -> list:
    """
    Retrieve and evaluate all positions for a given candidate, returning various similarity scores and analyses.
    Parameters
    ----------
    candidate_id : str
        The unique identifier of the candidate.
    Returns
    -------
    list
        A list of dictionaries, each containing:
            - position_id: str
            - candidate_id: str
            - pos_info_roleName: str
            - pos_info_positionName: str
            - cosine_similarity: float
            - cosine_similarity_sparse: float
            - custom_analysis: object
            - custom_analysis_1: object
            - analysis: dict
            - type: str ("info_db" or "summary_db")
    Raises
    ------
    HTTPException
        If the candidate or their embedding data is not found.
    """
    candidate = get_candidate_by_id(candidate_id)
    if candidate is None:
        raise HTTPException(status_code=404, detail="Candidate not found")

    embed_text, embedding_vector, sparse_embedding = get_candidate_embedding_data_by_id(candidate_id)
    if not embed_text or not embedding_vector or not sparse_embedding:
        raise HTTPException(status_code=404, detail="Candidate embedding data not found")

    positions = positions_for_candidate_id(candidate_id)
    # Order by cosine_similarity (position[2]) descending and take first 20
    positions = sorted(positions, key=lambda x: x[2] if x[2] is not None else 0, reverse=True)[:20]
    results = []

    for position in positions:
        position_id = position[0]
        position_info = position[1] or {}
        role_name = position_info.get('roleName', 'N/A')
        position_name = position_info.get('positionName', 'N/A')
        cosine_similarity = position[2] if len(position) > 2 else 0.0
        position_summary = str(position[3])
        position_embedding = position[4]
        position_sparse_embedding = position[5]
        pos_info_seniority = position_info.get('seniority', {}).get('name', 'N/A')

        # Calculate sparse cosine similarity
        cosine_sim_sparse = cosine_similarity_sparse(sparse_embedding, position_sparse_embedding)

        # Custom analysis using summaries
        custom_analysis_summaries = match_functions.get_candidate_analysis_custom_prompt(
            candidate_text=embed_text,
            processed_position=position_summary
        )
        print("custom_analysis_summaries", custom_analysis_summaries)
        results.append({
            "position_id": str(position_id),
            "candidate_id": candidate_id,
            "pos_info_roleName": role_name,
            "pos_info_positionName": position_name,
            "pos_info_seniority": pos_info_seniority,
            "cosine_similarity": round(float(cosine_similarity) * 100, 2) if cosine_similarity is not None else 0.0,
            "cosine_similarity_sparse": round(float(cosine_sim_sparse) * 100, 2) if cosine_sim_sparse is not None else 0.0,
            "custom_analysis": custom_analysis_summaries
        })

    if not results:
        logger.info("No positions found for candidate with ID: %s", candidate_id)
    # Convert custom_analysis to dict for sorting if needed
    def get_compatibility_percentage(x):
        custom_analysis = x.get("custom_analysis", {})
        if hasattr(custom_analysis, "dict"):
            custom_analysis = custom_analysis.dict()
        elif not isinstance(custom_analysis, dict):
            custom_analysis = vars(custom_analysis)
        return custom_analysis.get("compatibilityPercentage", 0)
    # Order by custom_analysis['compatibilityPercentage'] descending and take first 5
    results = sorted(
        results,
        key=get_compatibility_percentage,
        reverse=True
    )[:5]
    return results


# Retrieve positions from the database ordered by cosine similarity to the given candidate embedding.
def positions_for_candidate_embedding(candidate_embedding):
    """
    Retrieve positions from the database ordered by cosine similarity to the given candidate embedding.
    Parameters
    ----------
    candidate_embedding : list or array-like
        The embedding vector of the candidate to compare against position embeddings.
    Returns
    -------
    list of tuple
        Each tuple contains:
            - id: str
            - position_info: dict
            - cosine_similarity: float
            - to_be_embebbed: str
            - embedding: list
            - sparse_embedding: dict
    Notes
    -----
    The function uses the <=> operator for vector similarity in PostgreSQL and orders results
    by descending similarity (highest similarity first).
    """
    with get_cursor() as cur:
        cur.execute(
            """
            SELECT 
                id, 
                position_info, 
                1 - (embedding <=> %s) AS cosine_similarity, 
                to_be_embebbed, 
                embedding, 
                sparse_embedding
            FROM positions_smarthr
            ORDER BY cosine_similarity DESC
            """,
            (candidate_embedding,),
        )
        positions = cur.fetchall()
    logger.info("Positions found: %d", len(positions))
    return positions


# Retrieve positions from the database ordered by cosine similarity to the given candidate's embedding.
def positions_for_candidate_id(candidate_id: str):
    """
    Retrieve positions from the database ordered by cosine similarity to the given candidate's embedding.
    Parameters
    ----------
    candidate_id : str
        The unique identifier of the candidate.
    Returns
    -------
    list of tuple
        Each tuple contains:
            - id: str
            - position_info: dict
            - cosine_similarity: float
            - to_be_embebbed: str
            - embedding: list
            - sparse_embedding: dict
    Notes
    -----
    The function uses the <=> operator for vector similarity in PostgreSQL and orders results
    by descending similarity (highest similarity first).
    """
    query = """
        SELECT
            p.id,
            p.position_info,
            1 - (c.embedding <=> p.embedding) AS cosine_similarity,
            p.to_be_embebbed,
            p.embedding,
            p.sparse_embedding
        FROM public.positions_smarthr p,
            (SELECT embedding FROM public.candidates_smarthr WHERE id = %s) c
        ORDER BY cosine_similarity DESC
    """
    with get_cursor() as cur:
        cur.execute(query, (candidate_id,))
        positions = cur.fetchall()
    logger.info("Positions found for candidate_id %s: %d", candidate_id, len(positions))
    return positions


# Retrieve candidates from the database by their IDs, optionally ordered by cosine similarity to a given embedding.
def get_candidates_by_ids(candidate_ids: List[str], position_embedding: Optional[List[float]] = None) -> List[Tuple]:
    """
    Retrieve candidates from the database by their IDs, optionally ordered by cosine similarity to a given embedding.

    Parameters
    ----------
    candidate_ids : List[str]
        List of candidate UUID strings to fetch.
    position_embedding : Optional[List[float]], default=None
        If provided, results are ordered by cosine similarity to this embedding.

    Returns
    -------
    List[Tuple]
        List of tuples containing candidate data:
        (id, candidate_info, embedding, sparse_embedding, to_be_embebbed, proj_id[, cosine_similarity])
        If position_embedding is provided, cosine_similarity is included as the last element.

    Notes
    -----
    If no valid candidate IDs are provided, returns an empty list.
    """
    if not candidate_ids:
        logger.warning("No candidate IDs provided.")
        return []

    # Validate all IDs are valid UUID strings
    try:
        uuid_list = [str(uuid.UUID(cid)) for cid in candidate_ids]
    except Exception as exc:
        logger.error(f"Invalid candidate ID in input: {exc}")
        return []

    if position_embedding is not None:
        query = """
            SELECT id, candidate_info, embedding, sparse_embedding, to_be_embebbed, proj_id,
                   1 - (embedding <=> %s) AS cosine_similarity
            FROM public.candidates_smarthr
            WHERE id = ANY(%s::uuid[])
            ORDER BY cosine_similarity DESC
        """
        params = (position_embedding, uuid_list)
    else:
        query = """
            SELECT id, candidate_info, embedding, sparse_embedding, to_be_embebbed, proj_id
            FROM public.candidates_smarthr
            WHERE id = ANY(%s::uuid[])
        """
        params = (uuid_list,)

    try:
        with get_cursor() as cur:
            cur.execute(query, params)
            results = cur.fetchall()
        return results
    except Exception as exc:
        logger.error(f"Error fetching candidates by IDs: {str(exc)}")
        raise HTTPException(status_code=500, detail=f"Error fetching candidates by IDs: {str(exc)}")


# Get distinct created by from candidates
def get_created_by() -> List[str]:
    with get_cursor() as cur:
        cur.execute("""
            SELECT DISTINCT created_by
            FROM public.candidates_smarthr
            WHERE created_by IS NOT NULL AND TRIM(created_by) <> ''
        """)
        rows = cur.fetchall()
    return [row[0] for row in rows]
