# Standard library imports
import json
from contextlib import contextmanager
from typing import List, Optional
import logging

# Third-party imports
from datetime import datetime
import psycopg2
from psycopg2.extras import <PERSON><PERSON>
from fastapi import HTTPException
from langchain_core.messages import HumanMessage

# Internal imports
from config.config import MODELS_CONFIG
from core.config import settings
from models.enums import StatusInterview
from models.llm import inference_with_fallback, get_related_class_definitions
from models.interview import (
    InterviewCreate,
    InterviewProcessingRequest,
    ExtractedAnswers,
    ParaphrasedAnswers,
    ProcessType,
    QA_model,
    EvaluationResult,
    EvaluateInterviewNoQA,
    Interview,
    InterviewHr,
    InterviewTec,
)
from models.models import SingleQuestions
from controllers.positions_controller import get_position_by_id
from controllers.candidates_controller import get_candidate_by_id

# Telemetry Section
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


# Database connection context manager
@contextmanager
def get_cursor():
    """
    Context manager for PostgreSQL cursor with error handling.
    """
    conn = None
    try:
        conn = psycopg2.connect(
            settings.DATABASE_URL,
            connect_timeout=120,
            options="-c statement_timeout=120000",
            keepalives_idle=30
        )
        with conn:
            with conn.cursor() as cur:
                yield cur
    except psycopg2.Error as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    finally:
        if conn:
            conn.close()


# Helper function to build text for LLM prompts
# This function constructs a text prompt based on the provided items.
def get_topics(include: str) -> str:
    """
    This function constructs a text prompt based on the provided items.

    Args:
        include (str): A comma-separated string of items to include in the prompt.

    Returns:
        str: A formatted string based on the provided items.
    """
    base = ""
    desired_order = ['Technical Skills', 'Methodologies', 'Soft Skills', 'Language - Tools']

    # Map lowercase to original case
    lower_to_original = {item.lower(): item for item in desired_order}

    # Normalize input items to lowercase
    input_items = [item.strip().lower() for item in include.split(",") if item.strip()]

    if not input_items:
        return f"{base}{', '.join(desired_order)}."

    # Keep the order from desired_order and match only those present
    ordered = [lower_to_original[item.lower()] for item in desired_order if item.lower() in input_items]

    return f"{base}{', '.join(ordered)}."


# Core business logic for processing interviews
def process_interview(request: InterviewProcessingRequest):
    """
    This function processes an interview transcript based on the specified request type.

    Args:
        request (InterviewProcessingRequest): The request object containing questions, transcript, and process type.

    Returns:
        The processed result based on the request type.
    """
    if request.process_type == ProcessType.EXTRACT:
        schema = ExtractedAnswers
        task_prompt = (
            "Extract direct answers from the transcript for each question. "
            "Return ONLY the candidate's responses matching each question in order. "
            "IMPORTANT: If the transcript is empty, null, or contains insufficient content, "
            "return 'Invalid transcript' for each answer instead of generating fake responses."
        )
    else:
        schema = ParaphrasedAnswers
        task_prompt = (
            "Paraphrase the candidate's answers using the full context of the transcript, ensuring that:\n"
            "- The paraphrased answer remains faithful to what was actually said.\n"
            "- If relevant details appear in other parts of the transcript, include a 'complement_from' field.\n"
            "- Do NOT introduce new information or modify qualifications.\n"
            "- Return JSON following the provided schema."
        )

    user_msg = HumanMessage(
        content="Questions:\n"
        + "\n".join(f"{i + 1}. {q}" for i, q in enumerate(request.questions))
        + "\n\nTranscript:\n"
        + request.transcript
    )

    schema_text = get_related_class_definitions(schema)
    result = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=schema,
        user_messages=[user_msg],
        model_schema_text=schema_text,
        models_order=MODELS_CONFIG["default_models_order"],
    )
    if not result:
        raise RuntimeError("All LLM providers failed")
    return result  


# Helper function to run and persist interview processing
def run_and_persist_interview(interview_id: str, process_type: ProcessType):
    """
    This function runs the interview processing and persists the results in the database.

    Args:
        interview_id (str): The ID of the interview to process.
        process_type (ProcessType): The type of processing to perform.

    Returns:
        The processed result based on the request type.
    """
    with get_cursor() as cur:
        cur.execute(
            """
            SELECT iq.data, i.transcript_tec
            FROM interview_questions iq
            JOIN interviews i ON iq.position_id = i.position_id
            WHERE i.id = %s;
            """,
            (interview_id,)
        )
        row = cur.fetchone()
    if not row:
        # raise HTTPException(status_code=404, detail="Interview or questionnaire not found")
        return None

    questionnaire, transcript = row
    questions = [q["question"] for q in questionnaire["questions"]]

    req = InterviewProcessingRequest(
        questions=questions, transcript=transcript, process_type=process_type
    )
    result = process_interview(req)

    with get_cursor() as cur:
        cur.execute(
            """
            UPDATE interviews
            SET anwers_data = %s,
                updated_at = NOW()
            WHERE id = %s;
            """,
            (Json(result.model_dump()), interview_id)
        )
    return result


def evaluate_interview_with_no_qa(interview_id: str) -> EvaluationResult:
    """
    Evaluate the interview transcript, candidate info and position info.
    Args:
        interview_id (str): The ID of the interview to evaluate.
    Returns:
        EvaluationResult: The evaluation result.
    """
    # We should compare transcript, candidate info and position info
    # interviews has position_id, candidate_id, id, transcript_hr
    # For position info we need to retrieve it from positions_smarthr
    # For candidate info we need to retrieve it from candidates_smarthr
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT p.position_info, c.candidate_info, i.transcript_hr
                FROM interviews i
                JOIN positions_smarthr p ON i.position_id = p.id
                JOIN candidates_smarthr c ON i.candidate_id = c.id
                WHERE i.id = %s;
                """,
                (interview_id,)
            )
            row = cur.fetchone()
            if not row:
                raise HTTPException(status_code=404, detail="Interview not found (evaluate_interview_with_no_qa)")
        position_info, candidate_info, transcript_hr = row

        # """ SCHEMA
        # class EvaluateInterviewNoQA(BaseModel):
        #     overall_seniority: Seniority
        #     percentage_of_match: float
        #     explanation: str
        # """
        task_prompt_no_questions = """
            Evaluate the interview transcript, candidate info and position info.
            Return JSON that matches the provided schema.
            IMPORTANT RULES:
            • Provide an overall_seniority (senior|mid|junior) based on the transcript and candidate info.
        """
        schema_text = get_related_class_definitions(EvaluateInterviewNoQA)
        result = inference_with_fallback(
            task_prompt=task_prompt_no_questions,
            model_schema=EvaluateInterviewNoQA,
            user_messages=[HumanMessage(content=json.dumps({'position_info': position_info, 'candidate_info': candidate_info, 'transcript': transcript_hr}, ensure_ascii=False))],
            model_schema_text=schema_text,
            models_order=MODELS_CONFIG["default_models_order"],
        )

        if not result:
            raise RuntimeError("LLM evaluation failed")

        with get_cursor() as cur:
            cur.execute(
                """
                UPDATE interviews
                SET interview_data = %s,
                    updated_at = NOW()
                WHERE id = %s;
                """,
                (Json(result.model_dump()), interview_id)
            )
        return result

    except Exception as e:
        # Log the error and raise an HTTPException
        print(f"Error occurred while evaluating interview without QA: {str(e)}")
        logger.error(f"Error occurred while evaluating interview without QA: {str(e)}")
        raise HTTPException(status_code=404, detail=f"Interview not found (except: evaluate_interview_with_no_qa): {str(e)}")


# Evaluate the interview by comparing candidate answers with expected answers
def re_evaluate_interview(interview_id: str) -> EvaluationResult:
    """
    Evaluate the interview by comparing candidate answers with expected answers.

    Args:
        interview_id (str): The ID of the interview to evaluate.

    Returns:
        EvaluationResult: The evaluation result.
    """
    # Validate if interview_questions are not filled but transcript_hr is filled if yes evaluate_interview_with_no_qa

    with get_cursor() as cur:
        cur.execute(
            """
            SELECT iq.data, i.anwers_data, i.feedback_tec, i.transcript_tec
            FROM interview_questions iq
            JOIN interviews i ON iq.position_id = i.position_id
            WHERE i.id = %s;
            """,
            (interview_id,)
        )
        row = cur.fetchone()
    if not row:
        return evaluate_interview_with_no_qa(interview_id)

    expected_data, actual_answers, feedback_tec, transcript_tec = row

    # If expected questions are missing or empty, fallback to evaluation without QA
    if expected_data is None or not expected_data.get('questions'):
        return evaluate_interview_with_no_qa(interview_id)
    # elif expected is not None and expected.get('questions'):
    #     run_and_persist_interview(interview_id, ProcessType.EXTRACT)
    ## End first validation

    task_prompt = """
        You are evaluating a candidate's answers in a technical job interview.
        Compare each candidate response to the expected answers provided for three levels: senior, mid, and junior.

        DATA SOURCES:
        • If the actual answers contain "Invalid transcript" but a transcript_excerpt is provided, ignore the invalid answers and extract the real answers directly from the transcript_excerpt.
        • If the actual answers are meaningful and valid, use them as the primary source.
        • Use the transcript_excerpt as additional context when available.

        For each question:
        • Determine if the question was answered (check both actual answers and transcript_excerpt).
        • If answered, assign detected_seniority based strictly on the quality and depth of the response.
        - The allowed values are only: 'senior', 'mid', 'junior'.
        - Never return any other value.
        • If the answer is missing or very poor, assign 'junior' as the minimum detected_seniority.
        • Provide a concise explanation for the detected_seniority.
        • If the question was unanswered, indicate it explicitly and assign 'junior'.

        Additionally, determine the overall_seniority of the candidate considering:
        • All answered questions and their detected_seniority.
        • The number of questions answered vs unanswered (more answered questions increase the overall seniority weight).
        • Comments, transcripts, and candidate information.

        IMPORTANT RULES:
        • Evaluate every question; do not skip any.
        • When actual answers say "Invalid transcript", look for real answers in the transcript_excerpt.
        • Provide detected_seniority for each question with a clear explanation.
        • Provide overall_seniority with an explanation, and ensure it is strictly 'senior', 'mid', or 'junior'.
        • Include an overall percentage_of_match reflecting the ratio of answered questions to total questions.
        • The same percentage_of_match must appear in the overall_explanation.
        • Output ONLY a valid JSON object matching the schema. Do not include text outside the JSON.
        • Maintain consistent labeling: 'senior', 'mid', 'junior'.
        • Format explanations clearly for each question and for the overall evaluation.
        """
    schema_text = get_related_class_definitions(EvaluationResult)

    # Prepare comprehensive context for LLM
    evaluation_context = {
        'expected': expected_data,
        'actual': actual_answers
    }

    # Add feedback comments if available
    if feedback_tec:
        evaluation_context['feedback_comments'] = feedback_tec

    # Add transcript excerpt if available (truncate if too long)
    if transcript_tec:
        transcript_excerpt = transcript_tec[:1000] + "..." if len(transcript_tec) > 1000 else transcript_tec
        evaluation_context['transcript_excerpt'] = transcript_excerpt

    result = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=EvaluationResult,
        user_messages=[HumanMessage(content=json.dumps(evaluation_context, ensure_ascii=False))],
        model_schema_text=schema_text,
        models_order=MODELS_CONFIG["default_models_order"],
    )
    if not result:
        raise RuntimeError("LLM evaluation failed")

    with get_cursor() as cur:
        cur.execute(
            """
            UPDATE interviews
            SET interview_data = %s,
                updated_at = NOW()
            WHERE id = %s;
            """,
            (Json(result.model_dump()), interview_id)
        )
    return result


# Generate and persist interview questions
# This function generates interview questions based on the position ID and persists them in the database.
def generate_and_persist_qa(position_id: str, n_questions: int, include: str, current_user: str) -> QA_model:
    """
    This function generates interview questions based on the position ID and persists them in the database.

    Args:
        position_id (str): The ID of the position for which to generate questions.
        n_questions (int): The number of questions to generate.
        include (str): A comma-separated string of topics to include in the questions.
        current_user (str): The current user generating the questions.

    Returns:
        QA_model: The generated questions and related data.
    """
    # 1) Fetch position
    position = get_position_by_id(position_id)
    if not position:
        raise HTTPException(status_code=404, detail="Position not found")

    # Check if questions already exist for this position
    with get_cursor() as cur:
        cur.execute(
            """
            SELECT data, allow_regeneration FROM interview_questions WHERE position_id = %s;
            """,
            (position_id,)
        )
        result = cur.fetchone()  # fetchone can return None
        allow_regeneration = True if not result or result[1] is None else result[1]
        if not allow_regeneration:
            # If questions exist and regeneration is not allowed, raise an error
            raise HTTPException(
                status_code=400,
                detail=f"Interview questions already exist for position {position_id} and regeneration is not allowed"
            )

    # Adjust the field names if your JSON differs.
    info: dict = position.position_info or {}
    full_description = json.dumps(info, ensure_ascii=False)

    topics_text = get_topics(include)
    # print("topics_text", topics_text)
    task_prompt = f"""
        You are tasked with creating a structured interview questionnaire designed to evaluate **technical and methodological skills** while clearly differentiating levels of seniority among candidates for a specific role.

        Role Description:
        {full_description}

        **Please generate exactly {n_questions} questions based on the following topics: {topics_text}. For each question, ensure the output includes:**

        1. A sequential question_number ranging from 1 to {n_questions}.
        2. A single tag indicating the specific topic addressed, selected exclusively from: {topics_text}.
        3. Three distinct answers that reflect different seniority levels:
        - junior_answer
        - mid_answer
        - senior_answer

        **Guidelines for Answer Construction (Chain of Thought per level):**

        - senior_answer: Highlight advanced concepts, frameworks, and strategies. Emphasize decision-making, scalability, efficiency, and alignment with business value. Conclude with measurable outcomes or impact on organizational objectives.  
        - mid_answer: Describe practical execution, tools, and methodologies in detail. Show structured problem-solving and collaboration. Conclude with how these practices improve workflows or contribute to project/team success.  
        - junior_answer: Cover foundational concepts, learning in practice, and hands-on skills. Emphasize adaptability, eagerness to learn, and contribution to immediate team objectives.  

        **Formatting Rules:**
        - Deliver the output strictly in JSON format with valid syntax.  
        - Each topic from {topics_text} must appear in at least one question.  
        - Each question must have exactly one tag.  
        - Do not combine tags (e.g., "SOFT SKILLS METHODOLOGIES" is prohibited).  
        - Ensure clear differentiation between junior, mid, and senior answers — avoid repetition or generic filler.  
        - Avoid referencing seniority explicitly (e.g., "As a junior…" or "With X years of experience").  
        - Keep answers professional, substantive, and business-relevant.

        **Example (Agile Methodologies — Sprint Planning):**  
        - Junior: Basic understanding of Agile/Scrum, learning task organization, showing how participation supports team collaboration.  
        - Mid: Refining backlog, coordinating with stakeholders, ensuring adaptability and efficiency in delivery.  
        - Senior: Driving strategic alignment, leading planning sessions, ensuring measurable improvements in delivery and business outcomes.  
        """
    
    schema_text = get_related_class_definitions(QA_model)

    qa = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=QA_model,
        user_messages=[HumanMessage(content="")],  # no extra user message needed
        model_schema_text=schema_text,
        models_order=MODELS_CONFIG["default_models_order"],
    )
    if not qa:
        raise RuntimeError("LLM failed to generate questionnaire")

    # 3) Persist
    with get_cursor() as cur:
        cur.execute(
            """
            INSERT INTO interview_questions 
                (position_id, data, created_by, created_at, updated_by, updated_at) 
            VALUES 
                (%s, %s, %s, NOW(), %s, NOW())
            ON CONFLICT (position_id) DO UPDATE
            SET 
                data = EXCLUDED.data,
                updated_by = EXCLUDED.updated_by,
                updated_at = NOW();
            """,
            (position_id, Json(qa.model_dump()), current_user, current_user)
        )
    return qa


# Create interviews for the given position and candidates
# This function creates interviews for the given position and candidates.
def create_interviews_for_position(position_id, analysis_data: list[InterviewCreate]) -> List[Interview]:
    """
    Create interviews for the given position and candidates.

    Args:
        position_id (str): The ID of the position for which to create interviews.
        analysis_data (list[InterviewCreate]): List of interview data for candidates.

    Returns:
        List[Interview]: List of created interview objects.
    """
    try:
        for data in analysis_data:
            candidate_id = data.candidate_id

            if not candidate_id:
                continue
            # Validate if candidate_id is a valid UUID
            if not isinstance(candidate_id, str) or len(candidate_id) != 36:
                continue
            # validate if candidate_id exists in candidates_smarthr table
            exist = get_candidate_by_id(candidate_id)
            if not exist:
                continue
            # Check if interview already exists for this candidate and position
            exist = fetch_interview_by_position_id_candidate_id(position_id, candidate_id)
            if exist:
                continue
            # Insert new interview
            with get_cursor() as cur:
                cur.execute(
                    """
                    INSERT INTO interviews 
                    (position_id, candidate_id, analysis_data, status_hr, status_tec, created_at, updated_at)
                    VALUES (%s, %s, %s, 'not_scheduled', 'not_scheduled', NOW(), NOW())
                    RETURNING id, position_id, candidate_id
                    """,
                    (
                        position_id,
                        candidate_id,
                        Json(data.analysis_data if data.analysis_data else {}),
                    ),
                )

        return fetch_all_interviews_by_position_id(position_id)
    except psycopg2.Error as e:
        print(f"Database error occurred while creating interview: {str(e)}")
        logger.error(f"Database error occurred while creating interview: {str(e)}")
        raise HTTPException(status_code=500, detail=f"create_interview.Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while creating interview: {str(e.detail)}")
        logger.error(f"HTTPException occurred while creating interview: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while creating interview: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Get all interviews by position ID
# This function fetches all interviews for a given position ID.
def fetch_all_interviews_by_position_id(position_id: str) -> List[Interview]:
    """
    Fetch all interviews for a given position ID.

    Args:
        position_id (str): The ID of the position for which to fetch interviews.

    Returns:
        List[Interview]: List of interview objects.
    """
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT i.id, i.position_id, i.candidate_id, i.feedback_hr, i.interview_date_hr, i.feedback_date_hr, i.status_hr,
                i.recommendation_hr, i.transcript_hr, i.feedback_tec, i.interview_date_tec, i.feedback_date_tec, i.status_tec, i.recommendation_tec,
                i.transcript_tec, i.anwers_data, i.interview_data, i.created_at, i.updated_at, c.candidate_info,
                i.recruiter_hr_id, i.scheduled_hr_id, i.recruiter_tec_id, i.scheduled_tec_id, i.analysis_data
                FROM interviews i 
                JOIN candidates_smarthr c ON i.candidate_id = c.id
                WHERE i.position_id::text=%s and c.is_deleted = false
                ORDER BY created_at DESC
                """,
                (position_id,),
            )
            rows = cur.fetchall()
        interviews = []
        for row in rows:
            position = get_position_by_id(str(row[1]))
            interviews.append(
                Interview(
                    id=str(row[0]),
                    position_id=str(row[1]),
                    candidate_id=str(row[2]),
                    candidate_info=row[19].get('personal_info', None),
                    position_info=position.position_info if position is not None else None,
                    feedback_hr=row[3],
                    interview_date_hr=row[4],
                    feedback_date_hr=row[5],
                    status_hr=row[6],
                    recommendation_hr=row[7],
                    transcript_hr=row[8],
                    feedback_tec=row[9],
                    interview_date_tec=row[10],
                    feedback_date_tec=row[11],
                    status_tec=row[12],
                    recommendation_tec=row[13],
                    transcript_tec=row[14],
                    anwers_data=row[15],
                    interview_data=row[16],
                    created_at=row[17],
                    updated_at=row[18],
                    recruiter_hr_id=row[20],
                    scheduled_hr_id=row[21],
                    recruiter_tec_id=row[22],
                    scheduled_tec_id=row[23],
                    analysis_data=row[24]
                )
            )
        return interviews
    except psycopg2.Error as e:
        print(f"Database error occurred while fetching interviews: {str(e)}")
        logger.error(f"Database error occurred while fetching interviews: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_all_interviews_by_position_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while fetching interviews: {str(e.detail)}")
        logger.error(f"HTTPException occurred while fetching interviews: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while fetching interviews: {str(e)}")
        logger.error(f"Error occurred while fetching interviews: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Update interview feedback for HR
# This function updates the interview feedback for HR.
def update_interview_hr(interviewData: InterviewHr) -> Interview:
    """
    Update the interview feedback for HR.

    Args:
        interviewData (InterviewHr): The interview data to update.

    Returns:
        Interview: The updated interview object.
    """
    feedback = fetch_interview_by_position_id_candidate_id(interviewData.position_id, interviewData.candidate_id)
    if not feedback:
        raise HTTPException(status_code=404, detail="Interview not found")

    if feedback.status_hr == StatusInterview.COMPLETED.value or feedback.status_hr == StatusInterview.CANCELLED.value:
        raise HTTPException(status_code=400, detail="Interview already completed or cancelled")

    sqlQuery = """
         UPDATE interviews SET
            feedback_hr = %s,
            recruiter_hr_id = %s,
            scheduled_hr_id = %s,
            interview_date_hr = %s,
            feedback_date_hr = %s,
            status_hr = %s,
            recommendation_hr = %s,
            transcript_hr = %s,
            updated_at = Now()
            Where position_id = %s and candidate_id = %s
                RETURNING id,    position_id,    candidate_id,    feedback_hr,    interview_date_hr,    feedback_date_hr,    status_hr,
            recommendation_hr,    transcript_hr,    feedback_tec,    interview_date_tec,    feedback_date_tec,    status_tec,    recommendation_tec,
            transcript_tec,    created_at,    updated_at, recruiter_hr_id, scheduled_hr_id, recruiter_tec_id, scheduled_tec_id, analysis_data
            """
    params = [
        Json(interviewData.feedback_hr),
        interviewData.recruiter_hr_id,
        interviewData.scheduled_hr_id,
        interviewData.interview_date_hr,
        interviewData.feedback_date_hr,
        interviewData.status_hr,
        interviewData.recommendation_hr,
        interviewData.transcript_hr,
        interviewData.position_id,
        interviewData.candidate_id
    ]

    with get_cursor() as cur:
        cur.execute(sqlQuery, params)
        row = cur.fetchone()

    if not row:
        return None

    return Interview(
        id=str(row[0]),
        position_id=str(row[1]),
        candidate_id=str(row[2]),
        feedback_hr=row[3],
        interview_date_hr=row[4],
        feedback_date_hr=row[5],
        status_hr=row[6],
        recommendation_hr=row[7],
        transcript_hr=row[8],
        feedback_tec=row[9],
        interview_date_tec=row[10],
        feedback_date_tec=row[11],
        status_tec=row[12],
        recommendation_tec=row[13],
        transcript_tec=row[14],
        created_at=row[15],
        updated_at=row[16],
        recruiter_hr_id=row[17],
        scheduled_hr_id=row[18],
        recruiter_tec_id=row[19],
        scheduled_tec_id=row[20],
        analysis_data=row[21]
    )


# Update interview feedback for the technical team
# This function updates the interview feedback for the technical team.
def update_interview_tec(interviewData: InterviewTec) -> Interview:
    """
     Update the interview feedback for the technical team.

     Args:
         interviewData (InterviewTec): The interview data to update.

     Returns:
         Interview: The updated interview object.
    """
    feedback = fetch_interview_by_position_id_candidate_id(interviewData.position_id, interviewData.candidate_id)
    if not feedback:
        raise HTTPException(status_code=404, detail="Interview not found (update_interview_tec)")

    if feedback.status_tec == StatusInterview.COMPLETED.value or feedback.status_tec == StatusInterview.CANCELLED.value:
        raise HTTPException(status_code=400, detail="Interview already completed or cancelled")

    sqlQuery = """
         UPDATE interviews SET            
            feedback_tec = %s,
            recruiter_tec_id = %s,
            scheduled_tec_id = %s,
            interview_date_tec = %s,
            feedback_date_tec = %s,
            status_tec = %s,
            recommendation_tec = %s,
            transcript_tec = %s,
            updated_at = Now()
            Where position_id = %s and candidate_id = %s
                RETURNING id,    position_id,    candidate_id,    feedback_hr,    interview_date_hr,    feedback_date_hr,    status_hr,
            recommendation_hr,    transcript_hr,    feedback_tec,    interview_date_tec,    feedback_date_tec,    status_tec,    recommendation_tec,
            transcript_tec,    created_at,    updated_at, recruiter_hr_id, scheduled_hr_id, recruiter_tec_id, scheduled_tec_id, analysis_data
            """
    params = [
        Json(interviewData.feedback_tec),
        interviewData.recruiter_tec_id,
        interviewData.scheduled_tec_id,
        interviewData.interview_date_tec,
        interviewData.feedback_date_tec,
        interviewData.status_tec,
        interviewData.recommendation_tec,
        interviewData.transcript_tec,
        interviewData.position_id,
        interviewData.candidate_id
    ]

    with get_cursor() as cur:
        cur.execute(sqlQuery, params)
        row = cur.fetchone()

    if not row:
        return None

    response = Interview(
        id=str(row[0]),
        position_id=str(row[1]),
        candidate_id=str(row[2]),
        feedback_hr=row[3],
        interview_date_hr=row[4],
        feedback_date_hr=row[5],
        status_hr=row[6],
        recommendation_hr=row[7],
        transcript_hr=row[8],
        feedback_tec=row[9],
        interview_date_tec=row[10],
        feedback_date_tec=row[11],
        status_tec=row[12],
        recommendation_tec=row[13],
        transcript_tec=row[14],
        created_at=row[15],
        updated_at=row[16],
        recruiter_hr_id=row[17],
        scheduled_hr_id=row[18],
        recruiter_tec_id=row[19],
        scheduled_tec_id=row[20],
        analysis_data=row[21]
    )

    # fill anwers_data from transcript_tec after is completed
    if response.status_tec == StatusInterview.COMPLETED.value:
        run_and_persist_interview(response.id, ProcessType.EXTRACT)
        # evaluate: anwers_data against questionaire, and persist interview_data
        re_evaluate_interview(response.id)    

    return response


# Get a single interview by position ID and candidate ID
# This function fetches a single interview for the given position and candidate IDs.
def fetch_interview_by_position_id_candidate_id(position_id: str, candidate_id: str) -> Optional[Interview]:
    """
     Fetch a single interview for the given position and candidate IDs.

     Args:
         position_id (str): The ID of the position.
         candidate_id (str): The ID of the candidate.

     Returns:
         Optional[Interview]: The interview object if found, None otherwise.
    """
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT i.id, i.position_id, i.candidate_id, i.feedback_hr, i.interview_date_hr, i.feedback_date_hr, i.status_hr,
                i.recommendation_hr, i.transcript_hr, i.feedback_tec, i.interview_date_tec, i.feedback_date_tec, i.status_tec, i.recommendation_tec,
                i.transcript_tec, i.anwers_data, i.interview_data, i.created_at, i.updated_at, c.candidate_info, 
                i.recruiter_hr_id, i.scheduled_hr_id, i.recruiter_tec_id, i.scheduled_tec_id, i.analysis_data
                FROM interviews i 
                JOIN candidates_smarthr c ON i.candidate_id = c.id
                WHERE i.position_id::text=%s and i.candidate_id::text=%s and c.is_deleted = false
                ORDER BY created_at DESC
                """,
                (position_id, candidate_id,),
            )
            row = cur.fetchone()
        if not row:
            return None

        position = get_position_by_id(str(row[1]))
        return Interview(
            id=str(row[0]),
            position_id=str(row[1]),
            candidate_id=str(row[2]),
            candidate_info=row[19].get('personal_info', None),
            position_info=position.position_info if position is not None else None,
            feedback_hr=row[3],
            interview_date_hr=row[4],
            feedback_date_hr=row[5],
            status_hr=row[6],
            recommendation_hr=row[7],
            transcript_hr=row[8],
            feedback_tec=row[9],
            interview_date_tec=row[10],
            feedback_date_tec=row[11],
            status_tec=row[12],
            recommendation_tec=row[13],
            transcript_tec=row[14],
            anwers_data=row[15],
            interview_data=row[16],
            created_at=row[17],
            updated_at=row[18],
            recruiter_hr_id=row[20],
            scheduled_hr_id=row[21],
            recruiter_tec_id=row[22],
            scheduled_tec_id=row[23],
            analysis_data=row[24]
        )
    except psycopg2.Error as e:
        print(f"Database error occurred while fetching interviews: {str(e)}")
        logger.error(f"Database error occurred while fetching interviews: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_interview_by_position_id_candidate_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while fetching interviews: {str(e.detail)}")
        logger.error(f"HTTPException occurred while fetching interviews: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while fetching interviews: {str(e)}")
        logger.error(f"Error occurred while fetching interviews: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Get questions by position ID
# This function fetches the questions for the given position ID.
def fetch_questions_by_position_id(position_id: str) -> SingleQuestions:
    """
    Fetch the questions for the given position ID.

    Args:
        position_id (str): The ID of the position.

    Returns:
        SingleQuestions: The questions object for the specified position ID.
    """
    try:
        with get_cursor() as cur:
            sqlQuery = """
                SELECT id, position_id, data, created_at, updated_at, allow_regeneration, created_by, updated_by
                FROM interview_questions WHERE position_id::text=%s
            """
            params = (position_id,)
            cur.execute(sqlQuery, params)
            row = cur.fetchone()
            if not row:
                raise HTTPException(status_code=404, detail="Questions not found")
            return SingleQuestions(
                id=str(row[0]),
                position_id=str(row[1]),
                data=row[2],
                created_at=row[3],
                updated_at=row[4],
                allow_regeneration=row[5],
                created_by=row[6],
                updated_by=row[7]
            )
    except psycopg2.Error as e:
        print(f"Database error occurred while fetch_questions_by_position_id: {str(e)}")
        logger.error(f"Database error occurred while fetch_questions_by_position_id: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_questions_by_position_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while fetch_questions_by_position_id: {str(e.detail)}")
        logger.error(f"HTTPException occurred while fetch_questions_by_position_id: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while fetch_questions_by_position_id: {str(e)}")
        logger.error(f"Error occurred while fetch_questions_by_position_id: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Delete interview by position ID and candidate ID
# This function deletes an interview for the given position and candidate IDs.
def delete_interview(position_id: str, candidate_id: str) -> bool:
    """
    Delete an interview for the given position and candidate IDs.

    Args:
        position_id (str): The ID of the position.
        candidate_id (str): The ID of the candidate.

    Returns:
        bool: True if the interview was deleted successfully, False otherwise.
    """
    try:
        # Fetch the interview to check its status
        feedback = fetch_interview_by_position_id_candidate_id(position_id, candidate_id)
        if not feedback:
            raise HTTPException(status_code=404, detail="Interview not found")
        # You may add status checks here if needed

        with get_cursor() as cur:
            cur.execute(
                """
                DELETE FROM interviews WHERE id = %s
                """,
                (feedback.id,),
            )
        return True
    except psycopg2.Error as e:
        print(f"Database error occurred while deleting interview: {str(e)}")
        logger.error(f"Database error occurred while deleting interview: {str(e)}")
        raise HTTPException(status_code=500, detail=f"delete_interview. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while deleting interview: {str(e.detail)}")
        logger.error(f"HTTPException occurred while deleting interview: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while deleting interview: {str(e)}")
        logger.error(f"Error occurred while deleting interview: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Get all interviews by candidate ID
# This function fetches all interviews for the given candidate ID.
def fetch_interviews_by_candidate_id(candidate_id: str) -> List[Interview]:
    """
    Fetch all interviews for the given candidate ID.

    Args:
        candidate_id (str): The ID of the candidate for which to fetch interviews.

    Returns:
        List[Interview]: List of interview objects.
    """
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT i.id, i.position_id, i.candidate_id, i.feedback_hr, i.interview_date_hr, i.feedback_date_hr, i.status_hr,
                i.recommendation_hr, i.transcript_hr, i.feedback_tec, i.interview_date_tec, i.feedback_date_tec, i.status_tec, i.recommendation_tec,
                i.transcript_tec, i.anwers_data, i.interview_data, i.created_at, i.updated_at, c.candidate_info,
                i.recruiter_hr_id, i.scheduled_hr_id, i.recruiter_tec_id, i.scheduled_tec_id, i.analysis_data
                FROM interviews i 
                JOIN candidates_smarthr c ON i.candidate_id = c.id
                WHERE i.candidate_id::text=%s and c.is_deleted = false
                ORDER BY created_at DESC
                """,
                (candidate_id,),
            )
            rows = cur.fetchall()

        interviews: List[Interview] = []
        for row in rows:
            position = get_position_by_id(str(row[1]))
            interviews.append(
                Interview(
                    id=str(row[0]),
                    position_id=str(row[1]),
                    candidate_id=str(row[2]),
                    candidate_info=row[19].get('personal_info', None),
                    position_info=position.position_info if position is not None else None,
                    feedback_hr=row[3],
                    interview_date_hr=row[4],
                    feedback_date_hr=row[5],
                    status_hr=row[6],
                    recommendation_hr=row[7],
                    transcript_hr=row[8],
                    feedback_tec=row[9],
                    interview_date_tec=row[10],
                    feedback_date_tec=row[11],
                    status_tec=row[12],
                    recommendation_tec=row[13],
                    transcript_tec=row[14],
                    anwers_data=row[15],
                    interview_data=row[16],
                    created_at=row[17],
                    updated_at=row[18],
                    recruiter_hr_id=row[20],
                    scheduled_hr_id=row[21],
                    recruiter_tec_id=row[22],
                    scheduled_tec_id=row[23],
                    analysis_data=row[24]
                )
            )
        return interviews
    except psycopg2.Error as e:
        print(f"Database error occurred while fetch_interviews_by_candidate_id: {str(e)}")
        logger.error(f"Database error occurred while fetch_interviews_by_candidate_id: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_interviews_by_candidate_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while fetch_interviews_by_candidate_id: {str(e.detail)}")
        logger.error(f"HTTPException occurred while fetch_interviews_by_candidate_id: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while fetch_interviews_by_candidate_id: {str(e)}")
        logger.error(f"Error occurred while fetch_interviews_by_candidate_id: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Fetch interview by interview ID
# This function fetches an interview by its ID.
def fetch_interview_by_interview_id(interview_id: str) -> Optional[Interview]:
    """
    Fetch an interview by its ID.

    Args:
        interview_id (str): The ID of the interview to fetch.

    Returns:
        Optional[Interview]: The interview object if found, None otherwise.
    """
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT i.id, i.position_id, i.candidate_id, i.feedback_hr, i.interview_date_hr, i.feedback_date_hr, i.status_hr,
                i.recommendation_hr, i.transcript_hr, i.feedback_tec, i.interview_date_tec, i.feedback_date_tec, i.status_tec, i.recommendation_tec,
                i.transcript_tec, i.anwers_data, i.interview_data, i.created_at, i.updated_at, c.candidate_info,
                i.recruiter_hr_id, i.scheduled_hr_id, i.recruiter_tec_id, i.scheduled_tec_id, i.analysis_data
                FROM interviews i 
                JOIN candidates_smarthr c ON i.candidate_id = c.id
                WHERE i.id::text=%s and c.is_deleted = false
                ORDER BY created_at DESC
                """,
                (interview_id,),
            )
            row = cur.fetchone()
        if not row:
            return None

        position = get_position_by_id(str(row[1]))
        return Interview(
            id=str(row[0]),
            position_id=str(row[1]),
            candidate_id=str(row[2]),
            candidate_info=row[19].get('personal_info', None),
            position_info=position.position_info if position is not None else None,
            feedback_hr=row[3],
            interview_date_hr=row[4],
            feedback_date_hr=row[5],
            status_hr=row[6],
            recommendation_hr=row[7],
            transcript_hr=row[8],
            feedback_tec=row[9],
            interview_date_tec=row[10],
            feedback_date_tec=row[11],
            status_tec=row[12],
            recommendation_tec=row[13],
            transcript_tec=row[14],
            anwers_data=row[15],
            interview_data=row[16],
            created_at=row[17],
            updated_at=row[18],
            recruiter_hr_id=row[20],
            scheduled_hr_id=row[21],
            recruiter_tec_id=row[22],
            scheduled_tec_id=row[23],
            analysis_data=row[24]
        )
    except psycopg2.Error as e:
        print(f"Database error occurred while fetch_interview_by_interview_id: {str(e)}")
        logger.error(f"Database error occurred while fetch_interview_by_interview_id: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_interview_by_interview_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while fetch_interview_by_interview_id: {str(e.detail)}")
        logger.error(f"HTTPException occurred while fetch_interview_by_interview_id: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while fetch_interview_by_interview_id: {str(e)}")
        logger.error(f"Error occurred while fetch_interview_by_interview_id: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Re-evaluate an interview by its ID
# This function re-evaluates an interview by its ID.
# It fetches the interview data, extract answers data if needed, evaluates it, and then fetches the updated interview data.
def re_evalute_interview(interview_id: str) -> Interview:
    """
    Re-evaluate an interview by its ID.

    Args:       
        interview_id (str): The ID of the interview to re-evaluate.

    Returns:
        Interview: The updated interview object.
    """
    # 1. Fetch the interview data
    interview = fetch_interview_by_interview_id(interview_id)
    if not interview:
        raise HTTPException(status_code=404, detail="Interview not found (re_evalute_interview)")

    if not interview.transcript_tec:
        raise HTTPException(status_code=400, detail="Technical Transcript not found")

    if not interview.anwers_data:
        # 2. Run and persist the interview
        run_and_persist_interview(interview.id, ProcessType.EXTRACT)

    # 3. Evaluate the interview
    re_evaluate_interview(interview.id)

    # 4. Fetch the updated interview data
    interview = fetch_interview_by_interview_id(interview.id)
    return interview


# Change Questions status
def update_question_regeneration_status(position_id: str, question_id: str, allow_regeneration: bool) -> bool:
    """
    Change the status of a question in the interview_questions table.
    :param position_id: The ID of the position to which the question belongs.
    :param question_id: The ID of the question to update.
    :param allow_regeneration: Boolean indicating whether regeneration is allowed.
    :return: True if the update was successful, False otherwise.
    """
    with get_cursor() as cur:
        cur.execute(
            """
            UPDATE interview_questions
            SET allow_regeneration = %s,
                updated_at = NOW()
            WHERE id = %s AND position_id = %s;
            """,
            (allow_regeneration, question_id, position_id)
        )
        if cur.rowcount == 0:
            raise HTTPException(status_code=404, detail="Question not found")
        return True
    return False
